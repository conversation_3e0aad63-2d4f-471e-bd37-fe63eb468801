# Laravel Portfolio - Production Deployment Guide

This guide will help you deploy your Laravel Portfolio application to a production environment.

## Prerequisites

- PHP 8.2 or higher
- Composer
- Node.js and npm
- MySQL or PostgreSQL database
- Web server (Apache/Nginx)
- Git

## Quick Deployment Steps

### 1. <PERSON>lone the Repository

```bash
git clone https://github.com/Gibz-Coder/Laravel-Portfolio.git
cd Laravel-Portfolio
```

### 2. Install Dependencies

```bash
# Install PHP dependencies
composer install --optimize-autoloader --no-dev

# Install Node.js dependencies
npm install

# Build production assets
npm run build
```

### 3. Environment Configuration

```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

Edit the `.env` file with your production settings:

```env
APP_NAME="Laravel Portfolio"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel_portfolio
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-email-password
MAIL_FROM_ADDRESS="<EMAIL>"
```

### 4. Database Setup

```bash
# Run database migrations
php artisan migrate --force

# (Optional) Seed the database with sample data
php artisan db:seed --force

# Create storage link
php artisan storage:link
```

### 5. Production Optimization

Run the production optimization script:

**Linux/Mac:**
```bash
chmod +x scripts/production-optimize.sh
./scripts/production-optimize.sh
```

**Windows:**
```cmd
scripts\production-optimize.bat
```

This script will:
- Clear all caches
- Optimize Composer autoloader
- Cache configuration, routes, and views
- Cache events
- Run Laravel optimization

### 6. Set Permissions

```bash
# Set proper permissions for storage and cache
chmod -R 775 storage
chmod -R 775 bootstrap/cache

# Make sure the web server can write to these directories
chown -R www-data:www-data storage bootstrap/cache
```

## Web Server Configuration

### Apache Configuration

Create a virtual host configuration:

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/Laravel-Portfolio/public
    
    <Directory /path/to/Laravel-Portfolio/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/portfolio_error.log
    CustomLog ${APACHE_LOG_DIR}/portfolio_access.log combined
</VirtualHost>
```

### Nginx Configuration

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/Laravel-Portfolio/public;
    
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    
    index index.php;
    
    charset utf-8;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }
    
    error_page 404 /index.php;
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

## Security Considerations

1. **Environment Variables**: Never commit `.env` files to version control
2. **File Permissions**: Ensure proper file permissions are set
3. **HTTPS**: Always use HTTPS in production
4. **Database**: Use strong database credentials
5. **Updates**: Keep Laravel and dependencies updated

## Monitoring and Maintenance

### Log Files
- Application logs: `storage/logs/laravel.log`
- Web server logs: Check your web server configuration

### Regular Maintenance
```bash
# Clear logs periodically
php artisan log:clear

# Update dependencies (test in staging first)
composer update
npm update

# Re-optimize after updates
./scripts/production-optimize.sh
```

## Troubleshooting

### Common Issues

1. **500 Internal Server Error**
   - Check file permissions
   - Verify `.env` configuration
   - Check error logs

2. **Database Connection Issues**
   - Verify database credentials in `.env`
   - Ensure database server is running
   - Check firewall settings

3. **Asset Loading Issues**
   - Run `npm run build` to rebuild assets
   - Clear browser cache
   - Check web server configuration

### Debug Mode
Never enable debug mode in production. If you need to debug:
1. Check `storage/logs/laravel.log`
2. Use proper logging instead of debug mode
3. Test in a staging environment

## Support

For issues and questions:
- Check the Laravel documentation: https://laravel.com/docs
- Review application logs
- Contact the development team

---

**Important**: Always test deployment procedures in a staging environment before deploying to production.
